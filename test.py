import urllib

import sklearn
import requests


IP_CHECK_URL = "http://api.myip.com/"
REQUEST_TIMEOUT = 10

session = requests.Session()
session.headers.update({"User-Agent": "Mozilla/5.0 (X11; Linux x86_64) Safari/537.36"})
session.trust_env = False  # ignore environment proxy/no_proxy vars

# ***********
# {"ip":"***********","country":"Canada","cc":"CA"}

# http://************:80

# *************:80
# **************:8888
# ***************:80
# ***********:80
# ************:80
# *************:80
# *************:28051
# ************:80
# *************:80
# *************:13113


http_proxies =["http://*************:80", "http://**************:8888"]
# session.proxies = {'http': http_proxies, 'https': []}

proxy = 'http://*************:80'
r = session.get(IP_CHECK_URL, proxies={"http": proxy}, timeout=REQUEST_TIMEOUT)
print(r.text)

# test geocoding
address = "6351 St Louis Drive, Ottawa, ON"
encoded_address = urllib.parse.quote(address)
url = f"https://geocoder.ca/?locate={encoded_address}&geoit=XML&json=1"

r = session.get(url, proxies={"http": proxy}, timeout=REQUEST_TIMEOUT)
print(r.text)




