import requests, time, csv, re, random, os
from bs4 import BeautifulSoup
import pandas as pd
from tqdm import tqdm
import urllib.parse

BASE = "https://www.zolo.ca/ottawa-real-estate"
PAGES_TO_PARSE = 5  # first two pages
REQUESTS_PER_PROXY = 10  # Number of requests per proxy before switching
PROXY_FILE = "proxies.txt"
PROGRESS_FILE = "last_processed_page.txt"

# User-Agent rotation pool
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
]

# Global proxy management variables
current_proxy_index = 0
proxy_request_count = 0
proxies_list = []

def load_proxies():
    """Load proxies from proxies.txt file"""
    global proxies_list
    try:
        with open(PROXY_FILE, 'r') as f:
            proxies_list = [line.strip() for line in f if line.strip()]
        print(f"✅ Loaded {len(proxies_list)} proxies from {PROXY_FILE}")
        return True
    except FileNotFoundError:
        print(f"❌ Proxy file {PROXY_FILE} not found. Running without proxies.")
        proxies_list = []
        return False
    except Exception as e:
        print(f"❌ Error loading proxies: {e}")
        proxies_list = []
        return False

def get_current_proxy():
    """Get current proxy configuration"""
    global current_proxy_index, proxy_request_count

    if not proxies_list:
        return None

    # Check if we need to switch to next proxy
    if proxy_request_count >= REQUESTS_PER_PROXY:
        current_proxy_index = (current_proxy_index + 1) % len(proxies_list)
        proxy_request_count = 0
        print(f"🔄 Switching to proxy {current_proxy_index + 1}/{len(proxies_list)}: {proxies_list[current_proxy_index]}")

    proxy_request_count += 1
    proxy_url = proxies_list[current_proxy_index]

    return {
        'http': f'http://{proxy_url}',
        'https': f'http://{proxy_url}'
    }

def switch_to_next_proxy():
    """Force switch to next proxy (used when current proxy fails)"""
    global current_proxy_index, proxy_request_count

    if not proxies_list:
        return None

    current_proxy_index = (current_proxy_index + 1) % len(proxies_list)
    proxy_request_count = 0
    print(f"⚠️  Switching to next proxy due to failure: {proxies_list[current_proxy_index]}")

    return {
        'http': f'http://{proxies_list[current_proxy_index]}',
        'https': f'http://{proxies_list[current_proxy_index]}'
    }

def load_last_processed_page():
    """Load the last processed page number from file"""
    try:
        with open(PROGRESS_FILE, 'r') as f:
            page_num = int(f.read().strip())
        print(f"📖 Resuming from page {page_num} (loaded from {PROGRESS_FILE})")
        return page_num
    except FileNotFoundError:
        print(f"📝 No progress file found. Starting from page 1")
        return 1
    except Exception as e:
        print(f"❌ Error loading progress: {e}. Starting from page 1")
        return 1

def save_last_processed_page(page_num):
    """Save the last successfully processed page number"""
    try:
        with open(PROGRESS_FILE, 'w') as f:
            f.write(str(page_num))
    except Exception as e:
        print(f"❌ Error saving progress: {e}")

def get_realistic_headers(referer=None, simple_mode=False):
    """Generate realistic browser headers with rotation"""
    user_agent = random.choice(USER_AGENTS)

    if simple_mode:
        # Simplified headers for better compatibility
        headers = {
            "User-Agent": user_agent,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive",
        }
    else:
        # Full realistic headers
        headers = {
            "User-Agent": user_agent,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "en-US,en;q=0.9",
            # "Accept-Encoding": "gzip, deflate, br",
            # "Accept-Charset": "utf-8, iso-8859-1;q=0.5",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none" if not referer else "same-origin",
            "Sec-Fetch-User": "?1",
            "Cache-Control": "max-age=0",
            "DNT": "1"
        }

    if referer:
        headers["Referer"] = referer

    return headers

def random_delay(min_delay=2, max_delay=8):
    """Add random delay between requests"""
    delay = random.uniform(min_delay, max_delay)
    time.sleep(delay)

def debug_response_content(response, url):
    """Debug function to check response content encoding"""
    print(f"\n🔍 Debug info for {url}:")
    print(f"   Status Code: {response.status_code}")
    print(f"   Content-Type: {response.headers.get('content-type', 'Unknown')}")
    print(f"   Content-Encoding: {response.headers.get('content-encoding', 'None')}")
    print(f"   Content Length: {len(response.content)} bytes")
    print(f"   Text Length: {len(response.text)} chars")
    print(f"   First 200 chars: {repr(response.text[:200])}")
    print(f"   Apparent Encoding: {response.apparent_encoding}")
    print(f"   Detected Encoding: {response.encoding}")
    return True

def make_request_with_backoff(url, headers, max_retries=2, base_delay=1):
    """Make HTTP request with proxy support and enhanced retry logic"""
    proxy_retries = 0
    max_proxy_retries = min(len(proxies_list), 3) if proxies_list else 0

    # First try with proxies if available
    while proxy_retries < max_proxy_retries:
        # Get current proxy
        proxy_config = get_current_proxy()

        for attempt in range(max_retries):
            try:
                # Create a session to handle compression properly
                session = requests.Session()

                # Make request with proxy
                response = session.get(url, headers=headers, proxies=proxy_config, timeout=20)

                if response.status_code == 200:
                    # Force proper encoding detection
                    if response.encoding is None or response.encoding == 'ISO-8859-1':
                        response.encoding = response.apparent_encoding or 'utf-8'

                    # Verify we got proper text content
                    try:
                        # Test if we can decode the content properly
                        content_test = response.text[:100]  # Test first 100 chars
                        if content_test and '<' in content_test:  # Basic HTML check
                            return response
                        else:
                            print(f"Warning: Received non-HTML content from {url}")
                            if attempt == 0:  # Only debug on first attempt
                                debug_response_content(response, url)
                            # Sleep 2 seconds and try again
                            time.sleep(2)
                            continue
                    except UnicodeDecodeError as e:
                        print(f"Warning: Could not decode response from {url}: {e}")
                        if attempt == 0:  # Only debug on first attempt
                            debug_response_content(response, url)
                        # Sleep 2 seconds and try again
                        time.sleep(2)
                        continue

                elif response.status_code == 403:  # Forbidden - try simpler headers
                    if attempt == 0:
                        print(f"403 Forbidden - trying with simpler headers on retry")
                    wait_time = base_delay * (2 ** attempt) + random.uniform(1, 3)
                    time.sleep(wait_time)

                elif response.status_code == 429:  # Rate limited
                    wait_time = base_delay * (2 ** attempt) + random.uniform(0, 1)
                    print(f"Rate limited. Waiting {wait_time:.1f}s before retry {attempt + 1}/{max_retries}")
                    time.sleep(wait_time)

                elif response.status_code in [503, 502, 504]:  # Server errors
                    wait_time = base_delay * (2 ** attempt)
                    print(f"Server error {response.status_code}. Waiting {wait_time:.1f}s before retry {attempt + 1}/{max_retries}")
                    time.sleep(wait_time)
                else:
                    print(f"HTTP {response.status_code} error for {url}")
                    # Sleep 2 seconds and try again
                    time.sleep(2)
                    continue

            except requests.exceptions.RequestException as e:
                wait_time = base_delay * (2 ** attempt)
                print(f"Request failed: {e}. Waiting {wait_time:.1f}s before retry {attempt + 1}/{max_retries}")
                time.sleep(wait_time)

        # If we get here, all attempts with current proxy failed
        # Try switching to next proxy if available
        proxy_retries += 1
        if proxy_retries < max_proxy_retries and proxies_list:
            print(f"🔄 All attempts failed with current proxy. Trying next proxy...")
            proxy_config = switch_to_next_proxy()
            time.sleep(2)  # Wait 2 seconds before trying next proxy
        else:
            break

    # If all proxies failed or no proxies available, try without proxy as fallback
    print(f"🔄 Trying without proxy as fallback...")
    for attempt in range(max_retries):
        try:
            # Create a session to handle compression properly
            session = requests.Session()

            # Make request without proxy
            response = session.get(url, headers=headers, timeout=20)

            if response.status_code == 200:
                # Force proper encoding detection
                if response.encoding is None or response.encoding == 'ISO-8859-1':
                    response.encoding = response.apparent_encoding or 'utf-8'

                # Verify we got proper text content
                try:
                    # Test if we can decode the content properly
                    content_test = response.text[:100]  # Test first 100 chars
                    if content_test and '<' in content_test:  # Basic HTML check
                        return response
                    else:
                        print(f"Warning: Received non-HTML content from {url}")
                        if attempt == 0:  # Only debug on first attempt
                            debug_response_content(response, url)
                        # Sleep 2 seconds and try again
                        time.sleep(2)
                        continue
                except UnicodeDecodeError as e:
                    print(f"Warning: Could not decode response from {url}: {e}")
                    if attempt == 0:  # Only debug on first attempt
                        debug_response_content(response, url)
                    # Sleep 2 seconds and try again
                    time.sleep(2)
                    continue

            elif response.status_code == 403:  # Forbidden - try simpler headers
                if attempt == 0:
                    print(f"403 Forbidden - trying with simpler headers on retry")
                wait_time = base_delay * (2 ** attempt) + random.uniform(1, 3)
                time.sleep(wait_time)

            elif response.status_code == 429:  # Rate limited
                wait_time = base_delay * (2 ** attempt) + random.uniform(0, 1)
                print(f"Rate limited. Waiting {wait_time:.1f}s before retry {attempt + 1}/{max_retries}")
                time.sleep(wait_time)

            elif response.status_code in [503, 502, 504]:  # Server errors
                wait_time = base_delay * (2 ** attempt)
                print(f"Server error {response.status_code}. Waiting {wait_time:.1f}s before retry {attempt + 1}/{max_retries}")
                time.sleep(wait_time)
            else:
                print(f"HTTP {response.status_code} error for {url}")
                # Sleep 2 seconds and try again
                time.sleep(2)
                continue

        except requests.exceptions.RequestException as e:
            wait_time = base_delay * (2 ** attempt)
            print(f"Request failed: {e}. Waiting {wait_time:.1f}s before retry {attempt + 1}/{max_retries}")
            time.sleep(wait_time)

    print(f"Failed to fetch {url} after trying all proxies and direct connection")
    return None

def convert_beds_baths(value):
    """Convert beds/baths from format like '4+1' to '4.5'"""
    if value == "NA" or not value or value == "—":
        return "NA"

    # Handle formats like "2+1" -> "2.5"
    if "+" in value:
        parts = value.split("+")
        if len(parts) == 2 and parts[1].strip() == "1":
            return str(float(parts[0]) + 0.5)

    return value

def convert_sqft_range(sqft_text):
    """Convert sqft range like '1500-2000 sqft' to average number"""
    if sqft_text == "NA" or not sqft_text or sqft_text == "—":
        return "NA"

    # Remove 'sqft' and extract numbers
    sqft_clean = re.sub(r'\s*sqft.*', '', sqft_text)

    # Check if it's a range like "1500-2000"
    if "-" in sqft_clean:
        parts = sqft_clean.split("-")
        if len(parts) == 2:
            try:
                min_sqft = float(parts[0])
                max_sqft = float(parts[1])
                return str(int((min_sqft + max_sqft) / 2))
            except ValueError:
                pass

    # Try to extract just numbers if it's not a range
    numbers = re.findall(r'\d+', sqft_clean)
    if numbers:
        return numbers[0]

    return sqft_clean

def extract_price_number(price_text):
    """Extract numeric price from text like '$542,000' to '542000'"""
    if price_text == "NA" or not price_text:
        return "NA"

    # Remove dollar sign and commas
    price_clean = re.sub(r'[$,]', '', price_text)

    # Extract just the numbers
    numbers = re.findall(r'\d+', price_clean)
    if numbers:
        return numbers[0]

    return "NA"

def clean_mls_id(mls_text):
    """Extract MLS ID number from text like 'MLS® *********' to '*********'"""
    if not mls_text:
        return "NA"

    # Remove "MLS®" and whitespace
    mls_clean = re.sub(r'MLS®?\s*', '', mls_text).strip()
    return mls_clean if mls_clean else "NA"

def get_postal_code(address):
    return 'NA'

    """Get postal code from address using geocoder.ca API with enhanced error handling"""
    try:
        # Handle building numbers with dashes
        # Pattern: "2002-203 Catherine Street" -> "203 Catherine Street"
        if re.match(r'^\d+-\d+\s', address):
            parts = address.split('-', 1)
            if len(parts) > 1:
                address_for_lookup = parts[1].strip()
            else:
                address_for_lookup = address
        else:
            address_for_lookup = address

        encoded_address = urllib.parse.quote(address_for_lookup)
        url = f"https://geocoder.ca/?locate={encoded_address}&geoit=XML&json=1"

        # Use realistic headers for geocoder API
        headers = {
            "User-Agent": random.choice(USER_AGENTS),
            "Accept": "application/json, text/plain, */*",
            "Accept-Language": "en-US,en;q=0.9",
            "Connection": "keep-alive",
            "DNT": "1"
        }

        # Make request with shorter timeout for API calls
        response = make_request_with_backoff(url, headers, max_retries=2, base_delay=0.5)

        if response and response.status_code == 200:
            data = response.json()
            postal = data.get('postal', 'NA')

            # Add 3 second delay between geocoding requests as requested
            time.sleep(3)
            return postal
        else:
            # Still add delay even on failed requests to be respectful
            time.sleep(3)
            return 'NA'

    except Exception as e:
        # Silently handle errors to avoid cluttering progress output
        return 'NA'

records = []
total_listings_processed = 0

# Initialize proxy system
load_proxies()

# Load starting page from progress file
start_page = load_last_processed_page()
end_page = start_page + PAGES_TO_PARSE - 1

# Initialize progress bar
progress_bar = tqdm(total=PAGES_TO_PARSE, desc="Scraping pages", unit="page")

for page in range(start_page, end_page + 1):
    url = BASE + f"/page-{page}"

    # Set referer for natural browsing simulation
    referer = BASE if page == 1 else f"{BASE}/page-{page-1}"
    headers = get_realistic_headers(referer=referer)

    # Make request with backoff
    response = make_request_with_backoff(url, headers)

    if not response:
        progress_bar.set_postfix({"Status": f"Failed page {page}"})
        progress_bar.update(1)
        continue

    soup = BeautifulSoup(response.text, "html.parser")
    page_listings = 0

    # Process each listing on this page
    for card in soup.select("article"):
        # Get the address link to extract URL
        address_link = card.select_one("a[href*='/ottawa-real-estate/']")
        if not address_link:
            continue

        href = address_link.get('href', '')
        if href.startswith('http'):
            listing_url = href
        else:
            listing_url = "https://www.zolo.ca" + href

        # Extract address (remove neighbourhood)
        full_line = address_link.get_text(" ", strip=True)
        # Take only the address part (before the bullet if it exists)
        address = full_line.split("•")[0].strip()

        # Extract geo coordinates
        lat_meta = card.select_one('meta[itemprop="latitude"]')
        lng_meta = card.select_one('meta[itemprop="longitude"]')
        latitude = lat_meta.get('content', 'NA') if lat_meta else 'NA'
        longitude = lng_meta.get('content', 'NA') if lng_meta else 'NA'

        # Extract property type from SVG title
        property_type_svg = card.select_one('svg title')
        property_type = property_type_svg.get_text() if property_type_svg else 'NA'

        # Extract price from structured data
        price_span = card.select_one('span[itemprop="price"]')
        if price_span:
            price_value = price_span.get('value', 'NA')
        else:
            # Fallback to text extraction
            price_li = card.select_one('li.price')
            price_value = price_li.get_text() if price_li else 'NA'

        price_clean = extract_price_number(price_value)

        # Skip this listing if there's no valid price
        if price_clean == "NA":
            continue

        # Extract beds, baths, sqft from list items
        bullets = [li.get_text(" ", strip=True) for li in card.select("li")]
        beds_raw = next((re.sub(r"\s*bed.*", "", b) for b in bullets if "bed" in b), "NA")
        baths_raw = next((re.sub(r"\s*bath.*", "", b) for b in bullets if "bath" in b), "NA")
        sqft_raw = next((b for b in bullets if "sqft" in b), "NA")

        # Convert beds and baths
        beds = convert_beds_baths(beds_raw)
        baths = convert_beds_baths(baths_raw)
        sqft = convert_sqft_range(sqft_raw)

        # Extract MLS ID - using find with regex instead of deprecated :contains
        mls_tag = card.find(string=re.compile(r"MLS"))
        mls_id = clean_mls_id(mls_tag) if mls_tag else "NA"

        # Get postal code from address
        postal_code = get_postal_code(address)

        records.append({
            "address": address,
            "price": price_clean,
            "beds": beds,
            "baths": baths,
            "sqft": sqft,
            "property_type": property_type,
            "latitude": latitude,
            "longitude": longitude,
            "mls_id": mls_id,
            "postal_code": postal_code,
            "listing_url": listing_url,
        })

        page_listings += 1
        total_listings_processed += 1

    # Save progress after successful page processing
    save_last_processed_page(page + 1)  # Save next page to process

    # Update progress bar with current page stats
    progress_bar.set_postfix({
        "Page listings": page_listings,
        "Total listings": total_listings_processed,
        "Valid records": len(records)
    })
    progress_bar.update(1)

    # Random delay between pages to simulate human behavior
    if page < end_page:  # Don't delay after the last page
        random_delay(3, 10)

# Close progress bar
progress_bar.close()

# 👉 write to CSV
df = pd.DataFrame(records)

# Check if CSV file exists to determine if we need header
csv_file_path = "data/ottawa_real_estate.csv"
file_exists = os.path.exists(csv_file_path)

# Create data directory if it doesn't exist
os.makedirs("data", exist_ok=True)

df.to_csv(
    csv_file_path,
    mode="a",      # open in *append* mode
    header=not file_exists,  # Add header only if file doesn't exist
    index=False
)

# Print final summary
print(f"\n🎉 Scraping completed successfully!")
print(f"📊 Summary:")
print(f"   • Pages processed: {end_page - start_page + 1}")
print(f"   • Page range: {start_page} to {end_page}")
print(f"   • Total listings found: {total_listings_processed}")
print(f"   • Valid records saved: {len(df)}")
if len(df) > 0:
    print(f"   • Records with postal codes: {len(df[df['postal_code'] != 'NA'])}")
print(f"   • Output file: {csv_file_path}")
print(f"   • Next page to process: {end_page + 1}")

if len(df) > 0:
    print(f"\n📈 Data quality:")
    print(f"   • Average price: ${df['price'].astype(int).mean():,.0f}")
    print(f"   • Price range: ${df['price'].astype(int).min():,} - ${df['price'].astype(int).max():,}")
    print(f"   • Property types: {', '.join(df['property_type'].value_counts().head(3).index.tolist())}")
else:
    print(f"\n⚠️  No valid records were collected. Check the website structure or try again later.")
